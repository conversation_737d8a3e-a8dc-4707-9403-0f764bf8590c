.settings-section {
  background-color: var(--vscode-textBlockQuote-background);
  padding: 1rem;
  border-radius: 1rem;
}
.settings-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--vscode-editor-background);
  z-index: 100;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.header-with-back {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-button {
  background: none;
  border: none;
  color: var(--vscode-icon-foreground);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.2rem 0.5rem;
  line-height: 1;
  border-radius: 4px;
}

.back-button:hover {
  color: var(--vscode-foreground);
  background-color: var(--vscode-button-hoverBackground);
}

.close-button:hover {
  color: var(--vscode-foreground);
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.8rem;
  color: var(--vscode-foreground);
}

.form-group input {
  padding: 0.5rem;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border: 1px solid var(--vscode-input-border);
  border-radius: 2px;
  font-size: 0.8rem;
}

.form-group input:focus {
  outline: 1px solid var(--vscode-focusBorder);
  border-color: var(--vscode-focusBorder);
}

.button-group {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.button-group button {
  padding: 0.5rem 1rem;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 0.8rem;
}

.button-group button:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.button-group button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.stop-sync-button {
  background-color: var(--vscode-errorForeground);
  color: var(--vscode-editor-background);
}

.stop-sync-button:hover {
  background-color: var(--vscode-errorForeground);
  opacity: 0.9;
}

.resume-sync-button {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.resume-sync-button:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.resume-indexing-button {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  margin-top: 0.5rem;
}

.resume-indexing-button:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.status-message {
  padding: 0.5rem;
  border-radius: 0.25rem;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.status-message.success {
  background-color: rgba(0, 128, 0, 0.1);
  color: #2cbb5d;
  border: 1px solid #2cbb5d;
}

.status-message.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #f14c4c;
  border: 1px solid #f14c4c;
}

.sync-progress {
  margin-top: 1rem;
}

.progress-label {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: var(--vscode-panel-border);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--vscode-button-background);
  transition: width 0.3s ease;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--vscode-input-background);
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: '';
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: var(--vscode-button-background);
  transition: 0.4s;
}

input:checked + .slider {
  background-color: var(--vscode-button-background);
}

input:checked + .slider:before {
  background-color: var(--vscode-button-foreground);
  transform: translateX(20px);
}

.slider.round {
  border-radius: 20px;
}

.slider.round:before {
  border-radius: 50%;
}

.select-larger {
  height: 36px; /* Increased height */
  padding: 8px;
  font-size: 14px;
}

/* Style for the dropdown options */
.select-larger option {
  padding: 8px;
  height: 30px; /* Height for individual options */
  line-height: 30px; /* Helps with vertical alignment */
}

.model-select-container {
  position: relative;
  width: 100%;
}

.model-download-status {
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

/* .model-download-status .progress-bar {
  height: 4px;
  background-color: var(--vscode-progressBar-background);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.25rem;
} */

/* .model-download-status .progress-fill {
  height: 100%;
  background-color: var(--vscode-progressBar-foreground);
  transition: width 0.3s ease;
} */

.error-message {
  margin-top: 0.5rem;
  color: var(--vscode-errorForeground);
  font-size: 0.9rem;
  padding: 0.5rem;
  background-color: var(--vscode-errorBackground);
  border-radius: 4px;
}

.help-link {
  margin-left: 0.5rem;
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
  font-size: 0.9rem;
}

.help-link:hover {
  text-decoration: underline;
}

.progress-container .progress-text {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: var(--vscode-descriptionForeground);
}
