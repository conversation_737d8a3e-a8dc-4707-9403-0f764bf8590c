import { parentPort, workerData } from 'worker_threads';
import { createStructuredPrompt } from '../../utils/promptTemplates';
import { MODEL_PROVIDERS } from '../../../constants';
import OpenAI from 'openai';
import { EmbeddingSearchResult } from 'src/types/types';

interface WorkerData {
  prompt: string;
  searchResults: EmbeddingSearchResult[];
  modelId?: string;
  chatHistory?: string;
  provider?: string;
  apiKey?: string;
}

const {
  prompt,
  searchResults,
  modelId,
  chatHistory,
  provider,
  apiKey
} = workerData as WorkerData;

async function generateResponse(): Promise<void> {
  try {
    const structuredPrompt = createStructuredPrompt(searchResults, prompt, chatHistory);

    // Get provider configuration
    const providerConfig = MODEL_PROVIDERS.find(p => p.MODEL_PROVIDER === provider);
    if (!providerConfig || !modelId || !apiKey) {
      throw new Error(`Provider ${provider} or modelId not found`);
    }

    let responseText = await generateWithOpenAI(structuredPrompt, modelId, providerConfig.BASE_URL, apiKey);
    const cleanOutput = responseText.replace(/<think>[\s\S]*?<\/think>/g, '').trim();

    parentPort?.postMessage({
      type: 'response',
      content: cleanOutput
    });
  } catch (error) {
    parentPort?.postMessage({
      type: 'error',
      message: error instanceof Error ? error.message : String(error),
    });
  }
}

async function generateWithOpenAI(prompt: string, model: string, baseURL: string, apiKey: string): Promise<string> {
  try {
    const openai = new OpenAI({
      apiKey,
      baseURL,
    });

    const completion = await openai.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 512,
    });

    const content = completion.choices[0]?.message?.content;

    if (!content) {
      throw new Error('No text was generated by the model');
    }

    return content;
  } catch (error) {
    if (error instanceof OpenAI.APIError) {
      throw new Error(`OpenAI API error: ${error.status} ${error.message}`);
    }
    throw error;
  }
}

// Start processing
generateResponse();
