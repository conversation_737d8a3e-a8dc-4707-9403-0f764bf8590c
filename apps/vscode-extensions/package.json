{"name": "workspacegpt-extension", "displayName": "WorkspaceGPT", "description": "WorkspaceGPT is a fully local, privacy-first AI assistant for your development environment. Ask questions about your codebase or integrated Confluence documents — all without sending any data to external servers.", "version": "0.0.59", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "icon": "resources/icon.png", "repository": {"type": "git", "url": "https://github.com/ritesh-kant/workspaceGPT"}, "engines": {"vscode": "^1.87.0"}, "categories": ["Other"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "workspacegpt.ask", "title": "Ask WorkspaceGPT"}, {"command": "workspacegpt.newChat", "title": "New Chat", "icon": {"light": "resources/plus-dark.svg", "dark": "resources/plus-light.svg"}}, {"command": "workspacegpt.settings", "title": "Settings", "icon": {"light": "resources/settings-dark.svg", "dark": "resources/settings-light.svg"}}], "keybindings": [{"command": "workspacegpt.ask", "key": "ctrl+shift+r", "mac": "cmd+shift+r"}], "viewsContainers": {"activitybar": [{"id": "workspacegpt-sidebar", "title": "WorkspaceGPT", "icon": "resources/sidebar-icon.svg"}]}, "views": {"workspacegpt-sidebar": [{"type": "webview", "id": "workspacegpt.chatView", "name": "WorkspaceGPT", "icon": "resources/sidebar-icon.svg", "visibility": "visible"}]}, "viewsWelcome": [{"view": "workspacegpt.chatView", "contents": "Welcome to WorkspaceGPT"}], "menus": {"view/title": [{"command": "workspacegpt.newChat", "when": "view == workspacegpt.chatView", "group": "navigation@1"}, {"command": "workspacegpt.settings", "when": "view == workspacegpt.chatView", "group": "navigation@1"}]}, "configuration": {"title": "WorkspaceGPT", "properties": {}}}, "scripts": {"postinstall": "node scripts/post-install/patch-xenova-transformer.js", "vscode:prepublish": "pnpm run build", "build": "pnpm build-webview && node esbuild.config.js", "watch": "pnpm watch-webview && node esbuild.config.js --watch", "vscode:publish": "vsce publish --no-dependencies && pnpm create-tag", "vscode:publish-pre-release": "vsce publish --no-dependencies --pre-release && pnpm create-tag", "vscode:test": "vsce package --no-dependencies && trae --install-extension workspacegpt-extension-${npm_package_version}.vsix", "build-webview": "cd webview && pnpm install && pnpm run build", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "dev": "pnpm run build && code --new-window --extensionDevelopmentPath=${PWD}", "watch-webview": "cd webview && pnpm run dev", "create-tag": "git tag workspaceGPT-v${npm_package_version} && git push origin workspaceGPT-v${npm_package_version}"}, "dependencies": {"@huggingface/jinja": "^0.5.0", "@workspace-gpt/confluence-utils": "workspace:*", "@xenova/transformers": "^2.17.2", "glob": "^11.0.2", "markdown-it": "^14.1.0", "onnxruntime-common": "1.14.0", "onnxruntime-node": "1.14.0", "onnxruntime-web": "1.14.0", "openai": "^4.85.4", "posthog-node": "^5.1.0"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/markdown-it": "^14.1.2", "@types/node": "18.x", "@types/vscode": "^1.87.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "esbuild": "^0.20.1", "eslint": "^8.56.0", "typescript": "^5.3.3"}, "preview": false}